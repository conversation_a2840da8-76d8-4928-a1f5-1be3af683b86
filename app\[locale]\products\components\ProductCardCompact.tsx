"use client"

import { useState, useMemo } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useTranslations } from "@/hooks/useTranslations"
import Link from "next/link"
import {
  ArrowRight,
  Star,
  TrendingUp,
  CheckCircle,
  Clock,
  Brain,
  Cpu,
  GraduationCap,
  Code,
  Globe,
  Database,
  Target,
  Eye,
  BarChart,
  Users,
  Settings,
  Award,
  BookOpen,
  Building,
  Smartphone,
  Shield,
  Zap,
} from "lucide-react"

// Types
interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: Array<{ text: string; iconName: string }>
  highlight?: string
  price?: string
  category: string
}

interface ProductCardProps {
  product: Product
  index: number
}

interface ThemeColors {
  primary: string
  accent: string
  light: string
  cardBg: string
}

// Constants
const ICON_MAP = {
  Brain,
  Cpu,
  GraduationCap,
  Code,
  Globe,
  Database,
  Target,
  Eye,
  BarChart,
  Users,
  Settings,
  Award,
  BookOpen,
  Building,
  Smartphone,
  Shield,
  Zap,
  CheckCircle,
  Star,
  T<PERSON>ding<PERSON><PERSON>,
  Clock,
} as const

const CATEGORY_THEMES: Record<string, ThemeColors> = {
  AI服务: {
    primary: "from-blue-500 via-blue-600 to-indigo-600",
    accent: "#3B82F6",
    light: "rgba(59, 130, 246, 0.1)",
    cardBg: "from-blue-50/90 via-indigo-50/70 to-white/80",
  },
  云计算: {
    primary: "from-emerald-500 via-green-600 to-teal-600",
    accent: "#10B981",
    light: "rgba(16, 185, 129, 0.1)",
    cardBg: "from-emerald-50/90 via-green-50/70 to-white/80",
  },
  教育科技: {
    primary: "from-purple-500 via-violet-600 to-fuchsia-600",
    accent: "#8B5CF6",
    light: "rgba(139, 92, 246, 0.1)",
    cardBg: "from-purple-50/90 via-violet-50/70 to-white/80",
  },
  定制开发: {
    primary: "from-orange-500 via-red-600 to-pink-600",
    accent: "#F97316",
    light: "rgba(249, 115, 22, 0.1)",
    cardBg: "from-orange-50/90 via-red-50/70 to-white/80",
  },
}

const DEFAULT_THEME: ThemeColors = {
  primary: "from-slate-500 via-gray-600 to-zinc-600",
  accent: "#64748B",
  light: "rgba(100, 116, 139, 0.1)",
  cardBg: "from-slate-50/90 via-gray-50/70 to-white/80",
}

// Animation variants
const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  hover: { y: -4 },
}

const iconVariants = {
  hover: { scale: 1.05, rotate: 5 },
}

const titleVariants = {
  hover: { scale: 1.02 },
}

const tagVariants = {
  hover: { scale: 1.05 },
}

// Custom hooks
const useTheme = (category: string): ThemeColors => {
  return useMemo(() => CATEGORY_THEMES[category] || DEFAULT_THEME, [category])
}

// Components
const ProductIcon = ({
  iconName,
  className,
  theme,
}: {
  iconName: string
  className?: string
  theme: ThemeColors
}) => {
  const IconComponent = ICON_MAP[iconName as keyof typeof ICON_MAP]

  if (!IconComponent) {
    return <div className={cn("bg-slate-200 rounded", className)} />
  }

  return (
    <motion.div
      className="relative w-10 h-10 rounded-lg flex items-center justify-center shadow-md overflow-hidden flex-shrink-0"
      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
      variants={iconVariants}
      whileHover="hover"
      transition={{ duration: 0.3 }}
    >
      <IconComponent className={cn("w-5 h-5 text-white", className)} />
    </motion.div>
  )
}

const CategoryTag = ({ category, theme }: { category: string; theme: ThemeColors }) => (
  <motion.span
    className="px-2 py-0.5 text-xs font-medium rounded-md border flex-shrink-0"
    style={{
      color: theme.accent,
      background: "rgba(255, 255, 255, 0.8)",
      borderColor: theme.accent + "30",
    }}
    variants={tagVariants}
    whileHover="hover"
    transition={{ duration: 0.2 }}
  >
    {category}
  </motion.span>
)

const FeatureItem = ({
  feature,
  theme,
  index,
}: {
  feature: { text: string; iconName: string }
  theme: ThemeColors
  index: number
}) => {
  const IconComponent = ICON_MAP[feature.iconName as keyof typeof ICON_MAP]

  return (
    <motion.div
      className="flex items-center gap-2 p-2 rounded-lg bg-white/50 backdrop-blur-sm border border-white/30"
      initial={{ opacity: 0, x: -10 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{
        duration: 0.3,
        delay: index * 0.1,
      }}
      viewport={{ once: true }}
    >
      <div
        className="w-5 h-5 rounded-md flex items-center justify-center flex-shrink-0"
        style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
      >
        {IconComponent && <IconComponent className="h-3 w-3 text-white" />}
      </div>
      <span className="text-xs font-medium text-slate-700 flex-1 line-clamp-1">{feature.text}</span>
    </motion.div>
  )
}

const StatsSection = ({ price, theme }: { price?: string; theme: ThemeColors }) => (
  <div className="flex items-center justify-between p-2 rounded-lg bg-white/60 backdrop-blur-sm border border-white/40">
    <div className="flex items-center gap-2">
      <Star className="w-3 h-3 text-yellow-500 fill-current" />
      <span className="text-xs font-semibold text-slate-700">4.9</span>
    </div>
    {price && (
      <div className="text-right">
        <div className="text-xs font-bold" style={{ color: theme.accent }}>
          {price}
        </div>
      </div>
    )}
  </div>
)

const ActionButton = ({
  slug,
  theme,
  label,
}: {
  slug: string
  theme: ThemeColors
  label: string
}) => (
  <Button
    asChild
    className="w-full h-8 rounded-lg font-medium text-white shadow-md border-0 text-xs"
    style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
  >
    <Link href={`/products/${slug}`} className="flex items-center justify-center gap-2">
      <span>{label}</span>
      <ArrowRight className="h-3 w-3" />
    </Link>
  </Button>
)

// Main component
export function ProductCardCompact({ product, index }: ProductCardProps) {
  const t = useTranslations("productsPage")
  const tCard = useTranslations("productsPage.card")
  const [isHovered, setIsHovered] = useState(false)
  const theme = useTheme(product.category)

  const cardStyle = useMemo(
    () => ({
      background: `linear-gradient(135deg, ${theme.cardBg})`,
      borderColor: isHovered ? theme.accent : "rgba(255, 255, 255, 0.3)",
      boxShadow: isHovered ? `0 8px 32px ${theme.light}` : "0 4px 20px rgba(0, 0, 0, 0.1)",
    }),
    [theme, isHovered],
  )

  const topGradientStyle = useMemo(
    () => ({
      background: `linear-gradient(90deg, ${theme.primary})`,
    }),
    [theme.primary],
  )

  return (
    <motion.div
      className="group relative h-full"
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      transition={{
        duration: 0.5,
        delay: index * 0.1,
        type: "spring",
        stiffness: 100,
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div
        className={cn(
          "relative h-full rounded-2xl backdrop-blur-xl overflow-hidden",
          "border-2 transition-all duration-500",
          "shadow-md group-hover:shadow-xl",
          "bg-gradient-to-br",
        )}
        style={cardStyle}
      >
        {/* Top gradient bar */}
        <div className="absolute top-0 left-0 right-0 h-1 opacity-90" style={topGradientStyle} />

        {/* Card content */}
        <div className="relative z-10 p-4 h-full flex flex-col">
          {/* Header section */}
          <div className="flex items-start gap-3 mb-3">
            <ProductIcon iconName={product.iconName} theme={theme} />

            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2 mb-1">
                <motion.h3
                  className="text-base font-bold text-slate-900 leading-tight line-clamp-1"
                  variants={titleVariants}
                  whileHover="hover"
                  transition={{ duration: 0.2 }}
                >
                  {product.name}
                </motion.h3>

                {product.category && <CategoryTag category={product.category} theme={theme} />}
              </div>

              <p className="text-xs text-slate-600 leading-relaxed line-clamp-2">{product.description}</p>
            </div>
          </div>

          {/* Features section */}
          <div className="mb-3 flex-grow">
            <div className="space-y-2">
              {product.features.slice(0, 2).map((feature, featureIndex) => (
                <FeatureItem key={feature.text} feature={feature} theme={theme} index={featureIndex} />
              ))}
            </div>
          </div>

          {/* Stats section */}
          <div className="mb-3">
            <StatsSection price={product.price} theme={theme} />
          </div>

          {/* Action button */}
          <div className="mt-auto">
            <ActionButton slug={product.slug} theme={theme} label={tCard("learnMore")} />
          </div>
        </div>
      </div>
    </motion.div>
  )
}
